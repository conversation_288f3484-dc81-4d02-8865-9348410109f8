#!/usr/bin/env python3
"""
Test script for Google OAuth authentication endpoint
"""
import requests
import json

# Test configuration
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test if the server is running"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✅ Health check: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_google_auth_endpoint():
    """Test the Google OAuth endpoint structure"""
    try:
        # This will fail because we don't have a valid Google token,
        # but it will test if the endpoint exists and handles errors properly
        response = requests.post(
            f"{BASE_URL}/auth/google",
            json={"token": "invalid_token"},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📍 Google auth endpoint status: {response.status_code}")
        print(f"📍 Response: {response.text}")
        
        # We expect a 401 error with invalid token
        if response.status_code == 401:
            print("✅ Google auth endpoint is working (correctly rejecting invalid token)")
            return True
        else:
            print("⚠️  Unexpected response from Google auth endpoint")
            return False
            
    except Exception as e:
        print(f"❌ Google auth endpoint test failed: {e}")
        return False

def test_regular_auth():
    """Test regular email/password authentication still works"""
    try:
        # Test registration
        test_user = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = requests.post(
            f"{BASE_URL}/auth/register",
            json=test_user,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ Regular registration still works")
            token_data = response.json()
            
            # Test getting current user
            headers = {"Authorization": f"Bearer {token_data['access_token']}"}
            user_response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
            
            if user_response.status_code == 200:
                user_data = user_response.json()
                print(f"✅ User data retrieved: {user_data['email']}")
                print(f"📍 Provider: {user_data.get('provider', 'None')}")
                return True
            else:
                print("❌ Failed to get user data")
                return False
                
        elif response.status_code == 400 and "already registered" in response.text:
            print("✅ User already exists (registration working)")
            return True
        else:
            print(f"❌ Registration failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Regular auth test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Google OAuth Implementation")
    print("=" * 50)
    
    # Run tests
    tests = [
        ("Health Check", test_health_check),
        ("Google Auth Endpoint", test_google_auth_endpoint),
        ("Regular Auth", test_regular_auth),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Google OAuth backend is ready.")
    else:
        print("⚠️  Some tests failed. Check the implementation.")
