plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '14.2.0'

android {
  namespace "expo.modules.webbrowser"
  defaultConfig {
    versionCode 18
    versionName '14.2.0'
  }
}

dependencies {
  api "androidx.browser:browser:1.6.0"

  implementation "androidx.core:core-ktx:1.13.1"

  if (project.findProject(':expo-modules-test-core')) {
    testImplementation project(':expo-modules-test-core')
  }
  testImplementation "org.robolectric:robolectric:4.11.1"
}
