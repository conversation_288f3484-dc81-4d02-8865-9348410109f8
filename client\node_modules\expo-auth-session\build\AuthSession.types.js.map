{"version": 3, "file": "AuthSession.types.js", "sourceRoot": "", "sources": ["../src/AuthSession.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { AuthError } from './Errors';\nimport { TokenResponse } from './TokenRequest';\n\n// @needsAudit\n/**\n * Object returned after an auth request has completed.\n * - If the user cancelled the authentication session by closing the browser, the result is `{ type: 'cancel' }`.\n * - If the authentication is dismissed manually with `AuthSession.dismiss()`, the result is `{ type: 'dismiss' }`.\n * - If the authentication flow is successful, the result is `{ type: 'success', params: Object, event: Object }`.\n * - If the authentication flow is returns an error, the result is `{ type: 'error', params: Object, error: string, event: Object }`.\n */\nexport type AuthSessionResult =\n  | {\n      /**\n       * How the auth completed.\n       */\n      type: 'cancel' | 'dismiss' | 'opened' | 'locked';\n    }\n  | {\n      /**\n       * How the auth completed.\n       */\n      type: 'error' | 'success';\n      /**\n       * @deprecated Legacy error code query param, use `error` instead.\n       */\n      errorCode: string | null;\n      /**\n       * Possible error if the auth failed with type `error`.\n       */\n      error?: AuthError | null;\n      /**\n       * Query params from the `url` as an object.\n       */\n      params: Record<string, string>;\n      /**\n       * Returned when the auth finishes with an `access_token` property.\n       */\n      authentication: TokenResponse | null;\n      /**\n       * Auth URL that was opened\n       */\n      url: string;\n    };\n\n// @needsAudit\n/**\n * Options passed to `makeRedirectUri`.\n */\nexport type AuthSessionRedirectUriOptions = {\n  /**\n   * Optional path to append to a URI. This will not be added to `native`.\n   */\n  path?: string;\n  /**\n   * URI protocol `<scheme>://` that must be built into your native app.\n   */\n  scheme?: string;\n  /**\n   * Optional native scheme\n   * URI protocol `<scheme>://` that must be built into your native app.\n   */\n  queryParams?: Record<string, string | undefined>;\n  /**\n   * Should the URI be triple slashed `scheme:///path` or double slashed `scheme://path`.\n   * Defaults to `false`.\n   */\n  isTripleSlashed?: boolean;\n  /**\n   * Attempt to convert the Expo server IP address to localhost.\n   * This is useful for testing when your IP changes often, this will only work for iOS simulator.\n   *\n   * @default false\n   */\n  preferLocalhost?: boolean;\n  /**\n   * Manual scheme to use in Bare and Standalone native app contexts. Takes precedence over all other properties.\n   * You must define the URI scheme that will be used in a custom built native application or standalone Expo application.\n   * The value should conform to your native app's URI schemes.\n   * You can see conformance with `npx uri-scheme list`.\n   */\n  native?: string;\n};\n"]}