{"version": 3, "file": "createURL.js", "sourceRoot": "", "sources": ["../src/createURL.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,gBAAgB,CAAC;AAGvC,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,WAAW,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,SAAS,UAAU;IACjB,IAAI,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;QAClC,OAAO,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC;IACtC,CAAC;SAAM,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;QAC9B,mEAAmE;QACnE,gFAAgF;QAChF,OAAO,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;IACzE,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,SAAS,YAAY;IACnB,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;IAC7B,OAAO,CAAC,CAAC,CACP,OAAO;QACP,CAAC,6EAA6E,CAAC,IAAI,CAAC,OAAO,CAAC;YAC1F,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC,CACrC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,GAAW;IAC/B,OAAO,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,UAAU,CAAC,GAAW;IAC7B,OAAO,GAAG,CAAC,OAAO,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;AAChE,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,iCAAiC,CAAC,GAAW;IACpD,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAa,EAAE,YAAqB;IAC9D,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACvC,IAAI,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;SAAM,IAAI,CAAC,QAAQ,IAAI,YAAY,EAAE,CAAC;QACrC,OAAO,IAAI,KAAK,EAAE,CAAC;IACrB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,UAAU,SAAS,CACvB,IAAY,EACZ,EAAE,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,eAAe,GAAG,KAAK,KAAuB,EAAE;IAE5E,MAAM,cAAc,GAAG,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IAEjD,IAAI,OAAO,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC;IAEjC,IAAI,eAAe,EAAE,IAAI,YAAY,EAAE,EAAE,CAAC;QACxC,OAAO,GAAG,EAAE,CAAC;IACf,CAAC;IAED,IAAI,IAAI,EAAE,CAAC;QACT,IAAI,YAAY,EAAE,IAAI,OAAO,EAAE,CAAC;YAC9B,IAAI,GAAG,OAAO,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3C,CAAC;QACD,IAAI,eAAe,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7C,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,GAAG,EAAE,CAAC;IACZ,CAAC;IAED,6EAA6E;IAC7E,uBAAuB;IACvB,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,MAAM,sBAAsB,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAC3D,IAAI,sBAAsB,EAAE,CAAC;QAC3B,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACpC,WAAW,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC;YACH,iBAAiB,GAAG,MAAM,CAAC,WAAW;YACpC,wEAAwE;YACxE,IAAI,eAAe,CAAC,WAAW,CAAC,CACjC,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;QACV,WAAW,GAAG;YACZ,GAAG,WAAW;YACd,GAAG,iBAAiB;SACrB,CAAC;IACJ,CAAC;IACD,WAAW,GAAG,IAAI,eAAe;IAC/B,mFAAmF;IACnF,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,IAAI,IAAI,CAAuB,CACvF,CACF,CAAC,QAAQ,EAAE,CAAC;IACb,IAAI,WAAW,EAAE,CAAC;QAChB,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,CAAC;IAED,OAAO,GAAG,kBAAkB,CAAC,OAAO,EAAE,CAAC,eAAe,CAAC,CAAC;IAExD,uHAAuH;IACvH,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,cAAc,IAAI,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC,CAAC;IAClG,OAAO,GAAG,UAAU,GAAG,WAAW,EAAE,CAAC;AACvC,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,UAAU,KAAK,CAAC,GAAW;IAC/B,WAAW,CAAC,GAAG,CAAC,CAAC;IAEjB,MAAM,WAAW,GAA2B,EAAE,CAAC;IAC/C,IAAI,IAAI,GAAkB,IAAI,CAAC;IAC/B,IAAI,QAAQ,GAAkB,IAAI,CAAC;IACnC,IAAI,MAAM,GAAkB,IAAI,CAAC;IAEjC,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAE5B,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACzC,WAAW,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QACH,IAAI,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;QAC/B,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;QACnC,MAAM,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;IACnC,CAAC;IAAC,MAAM,CAAC;QACP,IAAI,GAAG,GAAG,CAAC;IACb,CAAC;IAED,MAAM,OAAO,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC;IACnC,MAAM,eAAe,GAAG,UAAU,CAAC,iCAAiC,CAAC,OAAO,CAAC,CAAC,CAAC;IAE/E,IAAI,MAAM,EAAE,CAAC;QACX,sBAAsB;QACtB,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,IAAI,EAAE,CAAC;QACT,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,UAAU,GAAkB,IAAI,CAAC;QACrC,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACzC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YACtF,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACzC,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAClC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,OAAO;QACL,QAAQ;QACR,IAAI;QACJ,WAAW;QACX,MAAM;KACP,CAAC;AACJ,CAAC", "sourcesContent": ["import Constants from 'expo-constants';\n\nimport { CreateURLOptions, ParsedURL } from './Linking.types';\nimport { hasCustomScheme, resolveScheme } from './Schemes';\nimport { validateURL } from './validateURL';\n\nfunction getHostUri(): string | null {\n  if (Constants.expoConfig?.hostUri) {\n    return Constants.expoConfig.hostUri;\n  } else if (!hasCustomScheme()) {\n    // we're probably not using up-to-date xdl, so just fake it for now\n    // we have to remove the /--/ on the end since this will be inserted again later\n    return removeScheme(Constants.linkingUri).replace(/\\/--($|\\/.*$)/, '');\n  } else {\n    return null;\n  }\n}\n\nfunction isExpoHosted(): boolean {\n  const hostUri = getHostUri();\n  return !!(\n    hostUri &&\n    (/^(.*\\.)?(expo\\.io|exp\\.host|exp\\.direct|expo\\.test|expo\\.dev)(:.*)?(\\/.*)?$/.test(hostUri) ||\n      Constants.expoGoConfig?.developer)\n  );\n}\n\nfunction removeScheme(url: string): string {\n  return url.replace(/^[a-zA-Z0-9+.-]+:\\/\\//, '');\n}\n\nfunction removePort(url: string): string {\n  return url.replace(/(?=([a-zA-Z0-9+.-]+:\\/\\/)?[^/]):\\d+/, '');\n}\n\nfunction removeLeadingSlash(url: string): string {\n  return url.replace(/^\\//, '');\n}\n\nfunction removeTrailingSlashAndQueryString(url: string): string {\n  return url.replace(/\\/?\\?.*$/, '');\n}\n\nfunction ensureLeadingSlash(input: string, shouldAppend: boolean): string {\n  const hasSlash = input.startsWith('/');\n  if (hasSlash && !shouldAppend) {\n    return input.substring(1);\n  } else if (!hasSlash && shouldAppend) {\n    return `/${input}`;\n  }\n  return input;\n}\n\n// @needsAudit\n/**\n * Helper method for constructing a deep link into your app, given an optional path and set of query\n * parameters. Creates a URI scheme with two slashes by default.\n *\n * The scheme must be defined in the [app config](./../config/app) under `expo.scheme`\n * or `expo.{android,ios}.scheme`. Platform-specific schemes defined under `expo.{android,ios}.scheme`\n * take precedence over universal schemes defined under `expo.scheme`.\n *\n * # Examples\n * - Development and production builds: `<scheme>://path` - uses the optional `scheme` property if provided, and otherwise uses the first scheme defined by your app config\n * - Web (dev): `https://localhost:19006/path`\n * - Web (prod): `https://myapp.com/path`\n * - Expo Go (dev): `exp://*********:8081/--/path`\n *\n * The behavior of this method in Expo Go for published updates is undefined and should not be relied upon.\n * The created URL in this case is neither stable nor predictable during the lifetime of the app.\n * If a stable URL is needed, for example in authorization callbacks, a build (or development build)\n * of your application should be used and the scheme provided.\n *\n * @param path Addition path components to append to the base URL.\n * @param namedParameters Additional options object.\n * @return A URL string which points to your app with the given deep link information.\n */\nexport function createURL(\n  path: string,\n  { scheme, queryParams = {}, isTripleSlashed = false }: CreateURLOptions = {}\n): string {\n  const resolvedScheme = resolveScheme({ scheme });\n\n  let hostUri = getHostUri() || '';\n\n  if (hasCustomScheme() && isExpoHosted()) {\n    hostUri = '';\n  }\n\n  if (path) {\n    if (isExpoHosted() && hostUri) {\n      path = `/--/${removeLeadingSlash(path)}`;\n    }\n    if (isTripleSlashed && !path.startsWith('/')) {\n      path = `/${path}`;\n    }\n  } else {\n    path = '';\n  }\n\n  // merge user-provided query params with any that were already in the hostUri\n  // e.g. release-channel\n  let queryString = '';\n  const queryStringMatchResult = hostUri.match(/(.*)\\?(.+)/);\n  if (queryStringMatchResult) {\n    hostUri = queryStringMatchResult[1];\n    queryString = queryStringMatchResult[2];\n    let paramsFromHostUri = {};\n    try {\n      paramsFromHostUri = Object.fromEntries(\n        // @ts-ignore: [Symbol.iterator] is indeed, available on every platform.\n        new URLSearchParams(queryString)\n      );\n    } catch {}\n    queryParams = {\n      ...queryParams,\n      ...paramsFromHostUri,\n    };\n  }\n  queryString = new URLSearchParams(\n    // For legacy purposes, we'll strip out the nullish values before creating the URL.\n    Object.fromEntries(\n      Object.entries(queryParams).filter(([, value]) => value != null) as [string, string][]\n    )\n  ).toString();\n  if (queryString) {\n    queryString = `?${queryString}`;\n  }\n\n  hostUri = ensureLeadingSlash(hostUri, !isTripleSlashed);\n\n  // URLSearchParams.stringify already encodes query parameters, so we only need to encode the remaining part of the URL.\n  const encodedURI = encodeURI(`${resolvedScheme}:${isTripleSlashed ? '/' : ''}/${hostUri}${path}`);\n  return `${encodedURI}${queryString}`;\n}\n\n// @needsAudit\n/**\n * Helper method for parsing out deep link information from a URL.\n * @param url A URL that points to the currently running experience (for example, an output of `Linking.createURL()`).\n * @return A `ParsedURL` object.\n */\nexport function parse(url: string): ParsedURL {\n  validateURL(url);\n\n  const queryParams: Record<string, string> = {};\n  let path: string | null = null;\n  let hostname: string | null = null;\n  let scheme: string | null = null;\n\n  try {\n    const parsed = new URL(url);\n\n    parsed.searchParams.forEach((value, key) => {\n      queryParams[key] = decodeURIComponent(value);\n    });\n    path = parsed.pathname || null;\n    hostname = parsed.hostname || null;\n    scheme = parsed.protocol || null;\n  } catch {\n    path = url;\n  }\n\n  const hostUri = getHostUri() || '';\n  const hostUriStripped = removePort(removeTrailingSlashAndQueryString(hostUri));\n\n  if (scheme) {\n    // Remove colon at end\n    scheme = scheme.substring(0, scheme.length - 1);\n  }\n\n  if (path) {\n    path = removeLeadingSlash(path);\n\n    let expoPrefix: string | null = null;\n    if (hostUriStripped) {\n      const parts = hostUriStripped.split('/');\n      expoPrefix = parts.slice(1).concat(['--/']).join('/');\n    }\n\n    if (isExpoHosted() && !hasCustomScheme() && expoPrefix && path.startsWith(expoPrefix)) {\n      path = path.substring(expoPrefix.length);\n      hostname = null;\n    } else if (path.indexOf('+') > -1) {\n      path = path.substring(path.indexOf('+') + 1);\n    }\n  }\n\n  return {\n    hostname,\n    path,\n    queryParams,\n    scheme,\n  };\n}\n"]}