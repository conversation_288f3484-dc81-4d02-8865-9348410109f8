{"version": 3, "file": "ExpoLinking.js", "sourceRoot": "", "sources": ["../src/ExpoLinking.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAgB,MAAM,mBAAmB,CAAC;AAUtE,MAAM,WAAW,GAAG,mBAAmB,CAA0B,aAAa,CAAC,CAAC;AAChF,eAAe,WAAW,CAAC", "sourcesContent": ["import { requireNativeModule, NativeModule } from 'expo-modules-core';\n\ntype ExpoLinkingModuleEvents = {\n  onURLReceived(url: string): void;\n};\n\ndeclare class ExpoLinkingNativeModule extends NativeModule<ExpoLinkingModuleEvents> {\n  getLinkingURL(): string | null;\n}\n\nconst ExpoLinking = requireNativeModule<ExpoLinkingNativeModule>('ExpoLinking');\nexport default ExpoLinking;\n"]}