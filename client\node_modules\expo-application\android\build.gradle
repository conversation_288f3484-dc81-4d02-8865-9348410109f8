plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '6.1.5'

android {
  namespace "expo.modules.application"
  defaultConfig {
    versionCode 12
    versionName '6.1.5'
  }
}

dependencies {
  implementation 'com.android.installreferrer:installreferrer:2.2'

  if (project.findProject(':expo-modules-test-core')) {
    testImplementation project(':expo-modules-test-core')
  }
  testImplementation "org.robolectric:robolectric:4.10"
  testImplementation 'junit:junit:4.13.2'
}
