{"version": 3, "file": "Application.types.js", "sourceRoot": "", "sources": ["../src/Application.types.ts"], "names": [], "mappings": "AAAA,eAAe;AACf;;GAEG;AACH,MAAM,CAAN,IAAY,sBAOX;AAPD,WAAY,sBAAsB;IAChC,yEAAW,CAAA;IACX,6EAAa,CAAA;IACb,+EAAc,CAAA;IACd,iFAAe,CAAA;IACf,uEAAU,CAAA;IACV,6EAAa,CAAA;AACf,CAAC,EAPW,sBAAsB,KAAtB,sBAAsB,QAOjC", "sourcesContent": ["// @docsMissing\n/**\n * @platform ios\n */\nexport enum ApplicationReleaseType {\n  UNKNOWN = 0,\n  SIMULATOR = 1,\n  ENTERPRISE = 2,\n  DEVELOPMENT = 3,\n  AD_HOC = 4,\n  APP_STORE = 5,\n}\n\n/**\n * Maps to the [`aps-environment`](https://developer.apple.com/documentation/bundleresources/entitlements/aps-environment) key in the native target's registered entitlements.\n * @platform ios\n */\nexport type PushNotificationServiceEnvironment = 'development' | 'production' | null;\n"]}