{"version": 3, "file": "PKCE.js", "sourceRoot": "", "sources": ["../src/PKCE.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,MAAM,aAAa,CAAC;AACtC,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,OAAO,GAAG,gEAAgE,CAAC;AAEjF,SAAS,qBAAqB,CAAC,MAAkB;IAC/C,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QACzC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAW;IACzC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACvE,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,IAAY;IACzC,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CAAC,IAAY;IACrD,6CAA6C;IAC7C,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,+BAA+B,CAAC,CAAC;IAElF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE;QACvF,QAAQ,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM;KACvC,CAAC,CAAC;IACH,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACxC,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,OAAe,GAAG;IAElB,kEAAkE;IAClE,MAAM,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IAC1C,MAAM,aAAa,GAAG,MAAM,oBAAoB,CAAC,YAAY,CAAC,CAAC;IAE/D,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAAC,IAAY;IACvD,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IACnC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE;QACxF,QAAQ,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG;KACpC,CAAC,CAAC;IACH,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACxC,CAAC", "sourcesContent": ["import * as Crypto from 'expo-crypto';\nimport invariant from 'invariant';\n\nconst CHARSET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n\nfunction convertBufferToString(buffer: Uint8Array): string {\n  const state: string[] = [];\n  for (let i = 0; i < buffer.byteLength; i += 1) {\n    const index = buffer[i] % CHARSET.length;\n    state.push(CHARSET[index]);\n  }\n  return state.join('');\n}\n\nfunction convertToUrlSafeString(b64: string): string {\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n}\n\nexport function generateRandom(size: number): string {\n  const buffer = Crypto.getRandomValues(new Uint8Array(size));\n  return convertBufferToString(buffer);\n}\n\n/**\n * Proof key for Code Exchange by OAuth Public Clients (RFC 7636), Section 4.1\n * [Section 4.1](https://tools.ietf.org/html/rfc7636#section-4.1)\n */\nexport async function deriveChallengeAsync(code: string): Promise<string> {\n  // 43 is the minimum, and 128 is the maximum.\n  invariant(code.length > 42 && code.length < 129, 'Invalid code length for PKCE.');\n\n  const buffer = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, code, {\n    encoding: Crypto.CryptoEncoding.BASE64,\n  });\n  return convertToUrlSafeString(buffer);\n}\n\nexport async function buildCodeAsync(\n  size: number = 128\n): Promise<{ codeChallenge: string; codeVerifier: string }> {\n  // This method needs to be resolved like all other native methods.\n  const codeVerifier = generateRandom(size);\n  const codeChallenge = await deriveChallengeAsync(codeVerifier);\n\n  return { codeVerifier, codeChallenge };\n}\n\n/**\n * Digest a random string with hex encoding, useful for creating `nonce`s.\n */\nexport async function generateHexStringAsync(size: number): Promise<string> {\n  const value = generateRandom(size);\n  const buffer = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, value, {\n    encoding: Crypto.CryptoEncoding.HEX,\n  });\n  return convertToUrlSafeString(buffer);\n}\n"]}