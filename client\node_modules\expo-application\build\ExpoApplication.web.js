export default {
    get applicationName() {
        return null;
    },
    get bundleId() {
        return null;
    },
    get nativeApplicationVersion() {
        return null;
    },
    get nativeBuildVersion() {
        return null;
    },
    get androidId() {
        return null;
    },
    async getInstallationTimeAsync() {
        return null;
    },
};
//# sourceMappingURL=ExpoApplication.web.js.map