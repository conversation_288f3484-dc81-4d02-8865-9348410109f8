{"version": 3, "file": "Linking.js", "sourceRoot": "", "sources": ["../src/Linking.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAC5C,OAAO,EAAuB,QAAQ,EAAE,MAAM,cAAc,CAAC;AAE7D,OAAO,WAAW,MAAM,eAAe,CAAC;AAExC,OAAO,SAAS,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,UAAU,gBAAgB,CAAC,IAAW,EAAE,OAAoB;IAChE,OAAO,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACnD,CAAC;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,aAAa,EAAE,CAAC;IACnD,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,IAAI;SAClB,CAAC;IACJ,CAAC;IAED,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC;AAC3B,CAAC;AAED,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,UAAU,CAAC,MAAc,EAAE,MAA2B;IAC1E,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;QAC9B,OAAO,MAAM,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IACD,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AACzD,CAAC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,YAAY;IAChC,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IAC3D,CAAC;IACD,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;QAC3B,OAAO,MAAM,SAAS,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;IACD,MAAM,OAAO,CAAC,eAAe,CAAC,CAAC;AACjC,CAAC;AAED,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa;IACjC,OAAO,CAAC,MAAM,SAAS,CAAC,aAAa,EAAE,CAAC,IAAI,IAAI,CAAC;AACnD,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,WAAW,CAAC,aAAa,EAAE,CAAC;AACrC,CAAC;AAED,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,KAAK,UAAU,OAAO,CAAC,GAAW;IACvC,WAAW,CAAC,GAAG,CAAC,CAAC;IACjB,OAAO,MAAM,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,UAAU,CAAC,GAAW;IAC1C,WAAW,CAAC,GAAG,CAAC,CAAC;IACjB,OAAO,MAAM,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACzC,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,UAAU,MAAM;IACpB,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAErD,SAAS,QAAQ,CAAC,KAAsB;QACtC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,SAAS,CAAC,GAAG,EAAE;QACb,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACvD,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;IACrC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,aAAa;IAC3B,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAgB,WAAW,CAAC,aAAa,CAAC,CAAC;IAE1E,SAAS,QAAQ,CAAC,KAAsB;QACtC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,WAAW,CAAC,WAAW,CAAC,eAAe,EAAE,QAAe,CAAC,CAAC;QAC/E,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;IACrC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,GAAG,IAAI,IAAI,CAAC;AACrB,CAAC;AAED,cAAc,iBAAiB,CAAC;AAChC,cAAc,WAAW,CAAC;AAC1B,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\nimport { useEffect, useState } from 'react';\nimport { EmitterSubscription, Platform } from 'react-native';\n\nimport ExpoLinking from './ExpoLinking';\nimport { ParsedURL, SendIntentExtras, URLListener } from './Linking.types';\nimport RNLinking from './RNLinking';\nimport { parse } from './createURL';\nimport { validateURL } from './validateURL';\n\n// @needsAudit\n/**\n * Add a handler to `Linking` changes by listening to the `url` event type and providing the handler.\n * It is recommended to use the [`useURL()`](#useurl) hook instead.\n * @param type The only valid type is `'url'`.\n * @param handler An [`URLListener`](#urllistener) function that takes an `event` object of the type\n * [`EventType`](#eventtype).\n * @return An EmitterSubscription that has the remove method from EventSubscription\n * @see [React Native documentation on Linking](https://reactnative.dev/docs/linking#addeventlistener).\n */\nexport function addEventListener(type: 'url', handler: URLListener): EmitterSubscription {\n  return RNLinking.addEventListener(type, handler);\n}\n\n// @needsAudit\n/**\n * Helper method which wraps React Native's `Linking.getInitialURL()` in `Linking.parse()`.\n * Parses the deep link information out of the URL used to open the experience initially.\n * If no link opened the app, all the fields will be `null`.\n * > On the web it parses the current window URL.\n * @return A promise that resolves with `ParsedURL` object.\n */\nexport async function parseInitialURLAsync(): Promise<ParsedURL> {\n  const initialUrl = await RNLinking.getInitialURL();\n  if (!initialUrl) {\n    return {\n      scheme: null,\n      hostname: null,\n      path: null,\n      queryParams: null,\n    };\n  }\n\n  return parse(initialUrl);\n}\n\n// @needsAudit\n/**\n * Launch an Android intent with extras.\n * > Use [`expo-intent-launcher`](./intent-launcher) instead. `sendIntent` is only included in\n * > `Linking` for API compatibility with React Native's Linking API.\n * @platform android\n */\nexport async function sendIntent(action: string, extras?: SendIntentExtras[]): Promise<void> {\n  if (Platform.OS === 'android') {\n    return await RNLinking.sendIntent(action, extras);\n  }\n  throw new UnavailabilityError('Linking', 'sendIntent');\n}\n\n// @needsAudit\n/**\n * Open the operating system settings app and displays the app’s custom settings, if it has any.\n */\nexport async function openSettings(): Promise<void> {\n  if (Platform.OS === 'web') {\n    throw new UnavailabilityError('Linking', 'openSettings');\n  }\n  if (RNLinking.openSettings) {\n    return await RNLinking.openSettings();\n  }\n  await openURL('app-settings:');\n}\n\n// @needsAudit\n/**\n * Get the URL that was used to launch the app if it was launched by a link.\n * @return The URL string that launched your app, or `null`.\n */\nexport async function getInitialURL(): Promise<string | null> {\n  return (await RNLinking.getInitialURL()) ?? null;\n}\n\n/**\n * Get the URL that was used to launch the app if it was launched by a link.\n * @return The URL string that launched your app, or `null`.\n */\nexport function getLinkingURL(): string | null {\n  return ExpoLinking.getLinkingURL();\n}\n\n// @needsAudit\n/**\n * Attempt to open the given URL with an installed app. See the [Linking guide](/guides/linking)\n * for more information.\n * @param url A URL for the operating system to open. For example: `tel:5555555`, `exp://`.\n * @return A `Promise` that is fulfilled with `true` if the link is opened operating system\n * automatically or the user confirms the prompt to open the link. The `Promise` rejects if there\n * are no applications registered for the URL or the user cancels the dialog.\n */\nexport async function openURL(url: string): Promise<true> {\n  validateURL(url);\n  return await RNLinking.openURL(url);\n}\n\n// @needsAudit\n/**\n * Determine whether or not an installed app can handle a given URL.\n * On web this always returns `true` because there is no API for detecting what URLs can be opened.\n * @param url The URL that you want to test can be opened.\n * @return A `Promise` object that is fulfilled with `true` if the URL can be handled, otherwise it\n * `false` if not.\n * The `Promise` will reject on Android if it was impossible to check if the URL can be opened, and\n * on iOS if you didn't [add the specific scheme in the `LSApplicationQueriesSchemes` key inside **Info.plist**](/guides/linking#linking-from-your-app).\n */\nexport async function canOpenURL(url: string): Promise<boolean> {\n  validateURL(url);\n  return await RNLinking.canOpenURL(url);\n}\n\n// @needsAudit\n/**\n * Returns the initial URL followed by any subsequent changes to the URL.\n * @deprecated Use `useLinkingURL` hook instead.\n * @return Returns the initial URL or `null`.\n */\nexport function useURL(): string | null {\n  const [url, setLink] = useState<string | null>(null);\n\n  function onChange(event: { url: string }) {\n    setLink(event.url);\n  }\n\n  useEffect(() => {\n    getInitialURL().then((url) => setLink(url));\n    const subscription = addEventListener('url', onChange);\n    return () => subscription.remove();\n  }, []);\n\n  return url;\n}\n\n/**\n * Returns the linking URL followed by any subsequent changes to the URL.\n * Always returns the initial URL immediately on reload.\n * @return Returns the initial URL or `null`.\n */\nexport function useLinkingURL(): string | null {\n  const [url, setLink] = useState<string | null>(ExpoLinking.getLinkingURL);\n\n  function onChange(event: { url: string }) {\n    setLink(event.url);\n  }\n\n  useEffect(() => {\n    const subscription = ExpoLinking.addListener('onURLReceived', onChange as any);\n    return () => subscription.remove();\n  }, []);\n\n  return url ?? null;\n}\n\nexport * from './Linking.types';\nexport * from './Schemes';\nexport { parse, createURL } from './createURL';\n"]}