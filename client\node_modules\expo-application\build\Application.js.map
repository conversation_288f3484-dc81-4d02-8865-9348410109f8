{"version": 3, "file": "Application.js", "sourceRoot": "", "sources": ["../src/Application.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAElE,OAAO,EAAE,sBAAsB,EAAsC,MAAM,qBAAqB,CAAC;AACjG,OAAO,eAAe,MAAM,mBAAmB,CAAC;AAEhD,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAkB,eAAe;IACpE,CAAC,CAAC,eAAe,CAAC,wBAAwB,IAAI,IAAI;IAClD,CAAC,CAAC,IAAI,CAAC;AAET,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAkB,eAAe;IAC9D,CAAC,CAAC,eAAe,CAAC,kBAAkB,IAAI,IAAI;IAC5C,CAAC,CAAC,IAAI,CAAC;AAET,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,eAAe,GAAkB,eAAe;IAC3D,CAAC,CAAC,eAAe,CAAC,eAAe,IAAI,IAAI;IACzC,CAAC,CAAC,IAAI,CAAC;AAET,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,MAAM,aAAa,GAAkB,eAAe;IACzD,CAAC,CAAC,eAAe,CAAC,aAAa,IAAI,IAAI;IACvC,CAAC,CAAC,IAAI,CAAC;AAET,cAAc;AACd;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,YAAY;IAC1B,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;QAC9B,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,eAAe,CAAC,SAAS,CAAC;AACnC,CAAC;AAED,cAAc;AACd;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB;IAC3C,IAAI,CAAC,eAAe,CAAC,uBAAuB,EAAE,CAAC;QAC7C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAC;IAC/E,CAAC;IACD,OAAO,MAAM,eAAe,CAAC,uBAAuB,EAAE,CAAC;AACzD,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB;IAC1C,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC;QAC5C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,wBAAwB,CAAC,CAAC;IAC9E,CAAC;IACD,OAAO,MAAM,eAAe,CAAC,sBAAsB,EAAE,CAAC;AACxD,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,iCAAiC;IACrD,IAAI,CAAC,eAAe,CAAC,8BAA8B,EAAE,CAAC;QACpD,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,gCAAgC,CAAC,CAAC;IACtF,CAAC;IACD,OAAO,MAAM,eAAe,CAAC,8BAA8B,EAAE,CAAC;AAChE,CAAC;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,6CAA6C;IACjE,IAAI,CAAC,eAAe,CAAC,0CAA0C,EAAE,CAAC;QAChE,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,4CAA4C,CAAC,CAAC;IAClG,CAAC;IACD,OAAO,MAAM,eAAe,CAAC,0CAA0C,EAAE,CAAC;AAC5E,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB;IAC5C,IAAI,CAAC,eAAe,CAAC,wBAAwB,EAAE,CAAC;QAC9C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,0BAA0B,CAAC,CAAC;IAChF,CAAC;IACD,MAAM,gBAAgB,GAAG,MAAM,eAAe,CAAC,wBAAwB,EAAE,CAAC;IAC1E,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpC,CAAC;AAED,cAAc;AACd;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB;IAC1C,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC;QAC5C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,wBAAwB,CAAC,CAAC;IAC9E,CAAC;IACD,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,sBAAsB,EAAE,CAAC;IACtE,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC;AAClC,CAAC;AAED,OAAO,EAAE,sBAAsB,EAAsC,CAAC", "sourcesContent": ["import { Platform, UnavailabilityError } from 'expo-modules-core';\n\nimport { ApplicationReleaseType, PushNotificationServiceEnvironment } from './Application.types';\nimport ExpoApplication from './ExpoApplication';\n\n// @needsAudit\n/**\n * The human-readable version of the native application that may be displayed in the app store.\n * At time when native app is built, on Android, this is the version name set by `version`\n * in app config, and on iOS, the `Info.plist` value for `CFBundleShortVersionString`.\n * On web, this value is `null`.\n * @example\n * `\"2.11.0\"`\n */\nexport const nativeApplicationVersion: string | null = ExpoApplication\n  ? ExpoApplication.nativeApplicationVersion || null\n  : null;\n\n// @needsAudit\n/**\n * The internal build version of the native application that the app stores may use to distinguish\n * between different binaries. At the time when native app is built, On Android, this is the version\n * code set by `android.versionCode` in app config, and on iOS, the `Info.plist` value for\n * `CFBundleVersion` (set with `ios.buildNumber` value in app config in a standalone app).\n * On web, this value is `null`. The return type on Android and iOS is `string`.\n * @example\n * Android: `\"114\"`, iOS: `\"2.11.0\"`\n */\nexport const nativeBuildVersion: string | null = ExpoApplication\n  ? ExpoApplication.nativeBuildVersion || null\n  : null;\n\n// @needsAudit\n/**\n * The human-readable name of the application that is displayed with the app's icon on the device's\n * home screen or desktop. On Android and iOS, this value is a `string` unless the name could not be\n * retrieved, in which case this value will be `null`. On web this value is `null`.\n * @example\n * `\"Expo\"`, `\"Yelp\"`, `\"Instagram\"`\n */\nexport const applicationName: string | null = ExpoApplication\n  ? ExpoApplication.applicationName || null\n  : null;\n\n// @needsAudit\n/**\n * The ID of the application. On Android, this is the application ID. On iOS, this is the bundle ID.\n * On web, this is `null`.\n * @example\n * `\"com.cocoacasts.scribbles\"`, `\"com.apple.Pages\"`\n */\nexport const applicationId: string | null = ExpoApplication\n  ? ExpoApplication.applicationId || null\n  : null;\n\n// @needsAudit\n/**\n * Gets the value of [`Settings.Secure.ANDROID_ID`](https://developer.android.com/reference/android/provider/Settings.Secure.html#ANDROID_ID).\n * This is a hexadecimal `string` unique to each combination of app-signing key, user, and device.\n * The value may change if a factory reset is performed on the device or if an APK signing key changes.\n * For more information about how the platform handles `ANDROID_ID` in Android 8.0 (API level 26)\n * and higher, see [Android 8.0 Behavior Changes](https://developer.android.com/about/versions/oreo/android-8.0-changes.html#privacy-all).\n * On iOS and web, this function is unavailable.\n * > In versions of the platform lower than Android 8.0 (API level 26), this value remains constant\n * > for the lifetime of the user's device. See the [ANDROID_ID](https://developer.android.com/reference/android/provider/Settings.Secure.html#ANDROID_ID)\n * > official docs for more information.\n * @example\n * `\"dd96dec43fb81c97\"`\n * @platform android\n */\nexport function getAndroidId(): string {\n  if (Platform.OS !== 'android') {\n    throw new UnavailabilityError('expo-application', 'androidId');\n  }\n  return ExpoApplication.androidId;\n}\n\n// @needsAudit\n/**\n * Gets the referrer URL of the installed app with the [`Install Referrer API`](https://developer.android.com/google/play/installreferrer)\n * from the Google Play Store. In practice, the referrer URL may not be a complete, absolute URL.\n * @return A `Promise` that fulfills with a `string` of the referrer URL of the installed app.\n *\n * @example\n * ```ts\n * await Application.getInstallReferrerAsync();\n * // \"utm_source=google-play&utm_medium=organic\"\n * ```\n * @platform android\n */\nexport async function getInstallReferrerAsync(): Promise<string> {\n  if (!ExpoApplication.getInstallReferrerAsync) {\n    throw new UnavailabilityError('expo-application', 'getInstallReferrerAsync');\n  }\n  return await ExpoApplication.getInstallReferrerAsync();\n}\n\n// @needsAudit\n/**\n * Gets the iOS \"identifier for vendor\" ([IDFV](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor))\n * value, a string ID that uniquely identifies a device to the app’s vendor. This method may\n * sometimes return `nil`, in which case wait and call the method again later. This might happen\n * when the device has been restarted before the user has unlocked the device.\n *\n * The OS will change the vendor identifier if all apps from the current app's vendor have been\n * uninstalled.\n *\n * @return A `Promise` that fulfills with a `string` specifying the app's vendor ID. Apps from the\n * same vendor will return the same ID. See Apple's documentation for more information about the\n * vendor ID's semantics.\n *\n * @example\n * ```ts\n * await Application.getIosIdForVendorAsync();\n * // \"68753A44-4D6F-1226-9C60-0050E4C00067\"\n * ```\n * @platform ios\n */\nexport async function getIosIdForVendorAsync(): Promise<string | null> {\n  if (!ExpoApplication.getIosIdForVendorAsync) {\n    throw new UnavailabilityError('expo-application', 'getIosIdForVendorAsync');\n  }\n  return await ExpoApplication.getIosIdForVendorAsync();\n}\n\n// @needsAudit\n/**\n * Gets the iOS application release type.\n * @return A `Promise` which fulfills with an [`ApplicationReleaseType`](#applicationreleasetype).\n * @platform ios\n */\nexport async function getIosApplicationReleaseTypeAsync(): Promise<ApplicationReleaseType> {\n  if (!ExpoApplication.getApplicationReleaseTypeAsync) {\n    throw new UnavailabilityError('expo-application', 'getApplicationReleaseTypeAsync');\n  }\n  return await ExpoApplication.getApplicationReleaseTypeAsync();\n}\n\n// @needsAudit\n/**\n * Gets the current [Apple Push Notification (APN)](https://developer.apple.com/documentation/bundleresources/entitlements/aps-environment?language=objc)\n * service environment.\n * @return A `Promise` that fulfills with the string, either `'development'` or `'production'`,\n * based on the current APN environment, or `null` on the simulator as it does not support registering with APNs.\n * @platform ios\n */\nexport async function getIosPushNotificationServiceEnvironmentAsync(): Promise<PushNotificationServiceEnvironment> {\n  if (!ExpoApplication.getPushNotificationServiceEnvironmentAsync) {\n    throw new UnavailabilityError('expo-application', 'getPushNotificationServiceEnvironmentAsync');\n  }\n  return await ExpoApplication.getPushNotificationServiceEnvironmentAsync();\n}\n\n// @needsAudit\n/**\n * Gets the time the app was installed onto the device, not counting subsequent updates. If the app\n * is uninstalled and reinstalled, this method returns the time the app was reinstalled.\n * - On Android, this method uses [`PackageInfo.firstInstallTime`](https://developer.android.com/reference/android/content/pm/PackageInfo.html#firstInstallTime).\n * - On iOS, this method uses the [`NSFileCreationDate`](https://developer.apple.com/documentation/foundation/nsfilecreationdate?language=objc)\n * of the app's document root directory.\n * - On web, this method returns `null`.\n *\n * @return A `Promise` that fulfills with a `Date` object that specifies the time the app\n * was installed on the device.\n *\n * @example\n * ```ts\n * await Application.getInstallationTimeAsync();\n * // 2019-07-18T18:08:26.121Z\n * ```\n */\nexport async function getInstallationTimeAsync(): Promise<Date> {\n  if (!ExpoApplication.getInstallationTimeAsync) {\n    throw new UnavailabilityError('expo-application', 'getInstallationTimeAsync');\n  }\n  const installationTime = await ExpoApplication.getInstallationTimeAsync();\n  return new Date(installationTime);\n}\n\n// @needsAudit\n/**\n * Gets the last time the app was updated from the Google Play Store.\n * @return A `Promise` that fulfills with a `Date` object that specifies the last time\n * the app was updated via the Google Play Store.\n *\n * @example\n * ```ts\n * await Application.getLastUpdateTimeAsync();\n * // 2019-07-18T21:20:16.887Z\n * ```\n * @platform android\n */\nexport async function getLastUpdateTimeAsync(): Promise<Date> {\n  if (!ExpoApplication.getLastUpdateTimeAsync) {\n    throw new UnavailabilityError('expo-application', 'getLastUpdateTimeAsync');\n  }\n  const lastUpdateTime = await ExpoApplication.getLastUpdateTimeAsync();\n  return new Date(lastUpdateTime);\n}\n\nexport { ApplicationReleaseType, PushNotificationServiceEnvironment };\n"]}