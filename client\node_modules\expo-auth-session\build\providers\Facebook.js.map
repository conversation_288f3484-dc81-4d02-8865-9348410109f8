{"version": 3, "file": "Facebook.js", "sourceRoot": "", "sources": ["../../src/providers/Facebook.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAChC,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAGxC,OAAO,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AACzE,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAA+C,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACjG,OAAO,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AACjF,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAGjD,OAAO,EAAE,sBAAsB,EAAE,MAAM,SAAS,CAAC;AAEjD,MAAM,QAAQ,GAAG;IACf,cAAc,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;IAC3C,kFAAkF;IAClF,aAAa,EAAE,CAAC,gBAAgB,EAAE,OAAO,CAAC;CAC3C,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAsB;IAC1C,qBAAqB,EAAE,4CAA4C;IACnE,aAAa,EAAE,oDAAoD;CACpE,CAAC;AAqBF,cAAc;AACd;;GAEG;AACH,MAAM,mBAAoB,SAAQ,WAAW;IAC3C,KAAK,CAAU;IAEf,YAAY,EACV,QAAQ;IACR,6DAA6D;IAC7D,WAAW,GAAG,EAAE,EAChB,YAAY,EACZ,GAAG,MAAM,EACiB;QAC1B,MAAM,WAAW,GAA2B;YAC1C,OAAO,EAAE,OAAO;YAChB,GAAG,WAAW;SACf,CAAC;QACF,IAAI,QAAQ,EAAE,CAAC;YACb,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC;QAChC,CAAC;QAED,2BAA2B;QAC3B,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC1E,IAAI,iBAAqC,CAAC;QAC1C,+DAA+D;QAC/D,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;YACrE,4EAA4E;YAC5E,iBAAiB,GAAG,YAAY,CAAC;QACnC,CAAC;QACD,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACzB,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC;QAC3C,CAAC;QACD,KAAK,CAAC;YACJ,GAAG,MAAM;YACT,YAAY,EAAE,iBAAiB;YAC/B,MAAM;YACN,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB;QAC7B,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,yBAAyB,EAAE,CAAC;QAChF,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAChB,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB,CAAC,EAAE,CAAC,CAAC;YAChD,CAAC;YACD,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;QACtC,CAAC;QACD,OAAO;YACL,GAAG,MAAM;YACT,WAAW;SACZ,CAAC;IACJ,CAAC;CACF;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,cAAc,CAC5B,SAA6C,EAAE,EAC/C,qBAA6D,EAAE;IAM/D,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,EAAE;QAC5B,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC;YACnC,GAAG,EAAE,aAAa;YAClB,OAAO,EAAE,iBAAiB;YAC1B,OAAO,EAAE,aAAa;SACd,CAAC,CAAC;QACZ,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC;QACzD,iBAAiB,CAAC,YAAY,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACtD,OAAO,QAAQ,CAAC;IAClB,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEtF,MAAM,WAAW,GAAG,OAAO,CAAC,GAAW,EAAE;QACvC,IAAI,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;YAC9C,OAAO,MAAM,CAAC,WAAW,CAAC;QAC5B,CAAC;QAED,OAAO,eAAe,CAAC;YACrB,qEAAqE;YACrE,MAAM,EAAE,KAAK,QAAQ,cAAc;YACnC,GAAG,kBAAkB;SACtB,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC;IAEvD,MAAM,WAAW,GAAG,OAAO,CAAC,GAA6C,EAAE;QACzE,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEnE,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;QAClC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE1C,MAAM,OAAO,GAAG,oBAAoB,CAClC;QACE,GAAG,MAAM;QACT,WAAW;QACX,QAAQ;QACR,WAAW;KACZ,EACD,SAAS,EACT,mBAAmB,CACpB,CAAC;IAEF,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE;QACrE,cAAc,EAAE,QAAQ,CAAC,cAAc;KACxC,CAAC,CAAC;IAEH,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AACxC,CAAC", "sourcesContent": ["import { useMemo } from 'react';\nimport { Platform } from 'react-native';\n\nimport { ProviderAuthRequestConfig } from './Provider.types';\nimport { applyRequiredScopes, invariantClientId } from './ProviderUtils';\nimport { AuthRequest } from '../AuthRequest';\nimport { AuthRequestConfig, AuthRequestPromptOptions, ResponseType } from '../AuthRequest.types';\nimport { useAuthRequestResult, useLoadedAuthRequest } from '../AuthRequestHooks';\nimport { makeRedirectUri } from '../AuthSession';\nimport { AuthSessionRedirectUriOptions, AuthSessionResult } from '../AuthSession.types';\nimport { DiscoveryDocument } from '../Discovery';\nimport { generateHexStringAsync } from '../PKCE';\n\nconst settings = {\n  windowFeatures: { width: 700, height: 600 },\n  // These are required for Firebase to work properly which is a reasonable default.\n  minimumScopes: ['public_profile', 'email'],\n};\n\nexport const discovery: DiscoveryDocument = {\n  authorizationEndpoint: 'https://www.facebook.com/v6.0/dialog/oauth',\n  tokenEndpoint: 'https://graph.facebook.com/v6.0/oauth/access_token',\n};\n\n// @needsAudit @docsMissing\n/**\n * @deprecated See [Facebook authentication](/guides/facebook-authentication/).\n */\nexport type FacebookAuthRequestConfig = ProviderAuthRequestConfig & {\n  /**\n   * Expo web client ID for use in the browser.\n   */\n  webClientId?: string;\n  /**\n   * iOS native client ID for use in development builds and bare workflow.\n   */\n  iosClientId?: string;\n  /**\n   * Android native client ID for use in development builds and bare workflow.\n   */\n  androidClientId?: string;\n};\n\n// @needsAudit\n/**\n * Extends [`AuthRequest`](#authrequest) and accepts [`FacebookAuthRequest`](#facebookauthrequest) in the constructor.\n */\nclass FacebookAuthRequest extends AuthRequest {\n  nonce?: string;\n\n  constructor({\n    language,\n    // Account selection cannot be reliably emulated on Facebook.\n    extraParams = {},\n    clientSecret,\n    ...config\n  }: FacebookAuthRequestConfig) {\n    const inputParams: Record<string, string> = {\n      display: 'popup',\n      ...extraParams,\n    };\n    if (language) {\n      inputParams.locale = language;\n    }\n\n    // Apply the default scopes\n    const scopes = applyRequiredScopes(config.scopes, settings.minimumScopes);\n    let inputClientSecret: string | undefined;\n    //  Facebook will throw if you attempt to use the client secret\n    if (config.responseType && config.responseType !== ResponseType.Code) {\n      // TODO: maybe warn that you shouldn't store the client secret on the client\n      inputClientSecret = clientSecret;\n    }\n    // Default to implicit auth\n    if (!config.responseType) {\n      config.responseType = ResponseType.Token;\n    }\n    super({\n      ...config,\n      clientSecret: inputClientSecret,\n      scopes,\n      extraParams: inputParams,\n    });\n  }\n\n  /**\n   * Load and return a valid auth request based on the input config.\n   */\n  async getAuthRequestConfigAsync(): Promise<AuthRequestConfig> {\n    const { extraParams = {}, ...config } = await super.getAuthRequestConfigAsync();\n    if (!extraParams.nonce && !this.nonce) {\n      if (!this.nonce) {\n        this.nonce = await generateHexStringAsync(16);\n      }\n      extraParams.auth_nonce = this.nonce;\n    }\n    return {\n      ...config,\n      extraParams,\n    };\n  }\n}\n\n/**\n * Load an authorization request.\n * Returns a loaded request, a response, and a prompt method.\n * When the prompt method completes then the response will be fulfilled.\n *\n * - [Get Started](https://docs.expo.dev/guides/authentication/#facebook)\n *\n * @param config\n * @param redirectUriOptions\n */\nexport function useAuthRequest(\n  config: Partial<FacebookAuthRequestConfig> = {},\n  redirectUriOptions: Partial<AuthSessionRedirectUriOptions> = {}\n): [\n  FacebookAuthRequest | null,\n  AuthSessionResult | null,\n  (options?: AuthRequestPromptOptions) => Promise<AuthSessionResult>,\n] {\n  const clientId = useMemo(() => {\n    const propertyName = Platform.select({\n      ios: 'iosClientId',\n      android: 'androidClientId',\n      default: 'webClientId',\n    } as const);\n    const clientId = config[propertyName] ?? config.clientId;\n    invariantClientId(propertyName, clientId, 'Facebook');\n    return clientId;\n  }, [config.iosClientId, config.androidClientId, config.webClientId, config.clientId]);\n\n  const redirectUri = useMemo((): string => {\n    if (typeof config.redirectUri !== 'undefined') {\n      return config.redirectUri;\n    }\n\n    return makeRedirectUri({\n      // The redirect URI should be created using fb + client ID on native.\n      native: `fb${clientId}://authorize`,\n      ...redirectUriOptions,\n    });\n  }, [clientId, config.redirectUri, redirectUriOptions]);\n\n  const extraParams = useMemo((): FacebookAuthRequestConfig['extraParams'] => {\n    const output = config.extraParams ? { ...config.extraParams } : {};\n\n    if (config.language) {\n      output.locale = config.language;\n    }\n    return output;\n  }, [config.extraParams, config.language]);\n\n  const request = useLoadedAuthRequest(\n    {\n      ...config,\n      extraParams,\n      clientId,\n      redirectUri,\n    },\n    discovery,\n    FacebookAuthRequest\n  );\n\n  const [result, promptAsync] = useAuthRequestResult(request, discovery, {\n    windowFeatures: settings.windowFeatures,\n  });\n\n  return [request, result, promptAsync];\n}\n"]}